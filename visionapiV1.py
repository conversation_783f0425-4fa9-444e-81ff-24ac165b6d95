import os
import json
import re
from typing import Optional
from pydantic import BaseModel, Field
from google.cloud import vision
from langchain.prompts import PromptTemplate
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_core.output_parsers import PydanticOutputParser
from langchain.schema.runnable import RunnableSequence

# ----------------------------------
# 🔐 API Keys & Auth
# ----------------------------------

# Google Vision API - Service Account Key
os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = "/Users/<USER>/Documents/hackathon/InsuranceOCR/ca-healthcareinsightsplatform-8509ba7b2e54.json"

# Gemini API Key
os.environ["GOOGLE_API_KEY"] = "AIzaSyCGa46rck1Z1ZLB8gf2bevoAuSACOwogl8"  # 👈 Replace with your Gemini API key


# ----------------------------------
# 📄 OCR with Google Vision API
# ----------------------------------

def detect_text(path):
    """Detects text in the file located in `path` using Google Vision API."""
    if not os.path.exists(path):
        raise FileNotFoundError(f"Image file not found: {path}")

    try:
        client = vision.ImageAnnotatorClient()

        with open(path, "rb") as image_file:
            content = image_file.read()

        image = vision.Image(content=content)
        response = client.document_text_detection(image=image)

        if response.error.message:
            raise Exception(f"Vision API Error: {response.error.message}")

        if not response.full_text_annotation:
            raise Exception("No text detected in the image")

        full_text = response.full_text_annotation.text
        print("\n✅ Full OCR Text:\n")
        print(full_text)
        print("\n" + "="*50 + "\n")

        return full_text

    except Exception as e:
        print(f"❌ OCR Error: {e}")
        raise


# ----------------------------------
# 🔧 Manual Extraction Fallback
# ----------------------------------

def extract_manual_fallback(text):
    """Manual extraction using regex patterns as fallback."""
    text_lower = text.lower()
    original_text = text  # Keep original for case-sensitive extractions

    # Define regex patterns for common insurance card fields
    patterns = {
        'member_id': [
            r'identification\s*number\s*:?\s*([a-z0-9]+)',
            r'member\s*(?:id|#)?\s*:?\s*([a-z0-9]+)',
            r'id\s*:?\s*([a-z0-9]+)',
            r'member\s*(?:number|no)?\s*:?\s*([a-z0-9]+)'
        ],
        'member_name': [
            r'subscriber\s*name\s*:?\s*([a-z\s\.]+)',
            r'member\s*name\s*:?\s*([a-z\s\.]+)',
            r'name\s*:?\s*([a-z\s\.]+)',
            r'patient\s*:?\s*([a-z\s\.]+)'
        ],
        'group_number': [
            r'group\s*(?:number|no|#)?\s*:?\s*([a-z0-9]+)',
            r'grp\s*:?\s*([a-z0-9]+)'
        ],
        'insurance_provider': [
            r'(bluecross\s*blueshield|blue\s*cross\s*blue\s*shield)',
            r'(aetna|cigna|humana|anthem|kaiser|united\s*healthcare)',
            r'(bcbs|uhc)'
        ],
        'plan_type': [
            r'network\s*:?\s*([^\\n]+(?:ppo|hmo|pos|epo)[^\\n]*)',
            r'plan\s*:?\s*(ppo|hmo|pos|epo)',
            r'(blue\s*choice\s*ppo)',
            r'(ppo|hmo|pos|epo)'
        ],
        'coverage_date': [
            r'coverage\s*date\s*:?\s*([0-9/]+)',
            r'effective\s*date\s*:?\s*([0-9/]+)'
        ],
        'copay_primary': [
            r'ov[^$]*\$([0-9]+)',
            r'primary\s*care[^$]*\$([0-9]+)',
            r'office\s*visit[^$]*\$([0-9]+)'
        ],
        'copay_specialist': [
            r'specialty[^$]*\$([0-9]+)',
            r'specialist[^$]*\$([0-9]+)'
        ],
        'copay_urgent': [
            r'urgent\s*care[^$]*\$([0-9]+)'
        ],
        'copay_emergency': [
            r'emergency\s*room[^$]*\$([0-9]+)',
            r'er[^$]*\$([0-9]+)'
        ],
        'rx_generic_copay': [
            r'rx\s*generic\s*copay[^$]*\$([0-9]+)',
            r'generic[^$]*\$([0-9]+)'
        ],
        'rx_brand_copay': [
            r'rx\s*brand\s*copay[^$]*\$([0-9/]+)',
            r'brand[^$]*\$([0-9/]+)'
        ],
        'rx_bin': [
            r'rxbin\s*:?\s*([0-9]+)',
            r'bin\s*:?\s*([0-9]+)'
        ],
        'rx_grp': [
            r'rxgrp\s*:?\s*([a-z0-9]+)',
            r'rx\s*grp\s*:?\s*([a-z0-9]+)',
            r'group\s*:?\s*([a-z0-9]+)'
        ],
        'rx_pcn': [
            r'rxpcn\s*:?\s*([a-z0-9]+)',
            r'pcn\s*:?\s*([a-z0-9]+)'
        ]
    }

    extracted = {}

    for field, field_patterns in patterns.items():
        for pattern in field_patterns:
            match = re.search(pattern, text_lower)
            if match:
                value = match.group(1).strip()
                # Keep original case for names and certain fields
                if field in ['member_name', 'insurance_provider']:
                    # Find the match in original text to preserve case
                    original_match = re.search(pattern, original_text, re.IGNORECASE)
                    if original_match:
                        value = original_match.group(1).strip()
                extracted[field] = value
                break

    return extracted


# ----------------------------------
# ✅ Define Pydantic Schema
# ----------------------------------

class InsuranceCardData(BaseModel):
    # Basic Member Information
    member_id: Optional[str] = Field(None, description="Member ID Number")
    member_name: Optional[str] = Field(None, description="Name of the member")
    group_number: Optional[str] = Field(None, description="Group number on the card")
    coverage_date: Optional[str] = Field(None, description="Coverage effective date")

    # Insurance Provider Information
    insurance_provider: Optional[str] = Field(None, description="Insurance company name (e.g., BlueCross BlueShield, Aetna, Cigna)")
    plan_type: Optional[str] = Field(None, description="Insurance plan type (e.g., PPO, HMO, Blue Choice PPO)")
    network: Optional[str] = Field(None, description="Network name or type")

    # Provider Information
    pcp_name: Optional[str] = Field(None, description="Primary Care Provider Name")
    pcp_phone: Optional[str] = Field(None, description="Primary Care Provider Phone Number")

    # Copay Information
    copay_primary: Optional[str] = Field(None, description="Co-pay for primary care visits")
    copay_specialist: Optional[str] = Field(None, description="Co-pay for specialist visits")
    copay_emergency: Optional[str] = Field(None, description="Co-pay for emergency care")
    copay_urgent: Optional[str] = Field(None, description="Co-pay for urgent care")

    # Prescription Drug Information (Detailed)
    rx_generic_copay: Optional[str] = Field(None, description="Generic prescription copay amount")
    rx_brand_copay: Optional[str] = Field(None, description="Brand prescription copay amount")
    rx_specialty_copay: Optional[str] = Field(None, description="Specialty prescription copay amount")
    rx_bin: Optional[str] = Field(None, description="RX BIN number for pharmacy processing")
    rx_grp: Optional[str] = Field(None, description="RX Group number for pharmacy processing")
    rx_pcn: Optional[str] = Field(None, description="RX PCN (Processor Control Number)")
    rx_id: Optional[str] = Field(None, description="RX ID number")

    # Deductible and Coverage Information
    in_network_deductible: Optional[str] = Field(None, description="In-network deductible and coinsurance")
    oon_deductible: Optional[str] = Field(None, description="Out-of-network deductible and coinsurance")

    # Contact Information
    website: Optional[str] = Field(None, description="Health plan website")
    contact_info: Optional[str] = Field(None, description="Plan contact information")
    customer_service_phone: Optional[str] = Field(None, description="Customer service phone number")


# ----------------------------------
# 🧠 LLM + Prompt + Parser Chain
# ----------------------------------

# Load Gemini LLM
llm = ChatGoogleGenerativeAI(model="models/gemini-2.5-flash-lite", temperature=0)

# Create parser from Pydantic schema
parser = PydanticOutputParser(pydantic_object=InsuranceCardData)

# Build prompt
prompt = PromptTemplate(
    template="""
You are a helpful assistant that extracts structured insurance card information from OCR text.
Extract the following fields from the OCR text and return them in the correct JSON format:

{format_instructions}

OCR TEXT:
\"\"\"
{ocr_text}
\"\"\"
""",
    input_variables=["ocr_text"],
    partial_variables={"format_instructions": parser.get_format_instructions()},
)

# Combine all into a runnable chain
chain: RunnableSequence = prompt | llm | parser


# ----------------------------------
# 🚀 Run It All Together
# ----------------------------------

def main():
    """Main function to process insurance card image."""
    # You can modify this path or make it a command line argument
    image_path = "/Users/<USER>/Downloads/Insurance Card.v1i.yolov7pytorch/train/images/IMG_2398_jpeg.rf.b19629dc9f57b55de251c710eec7f368.jpg"

    # Alternative: get image path from command line
    import sys
    if len(sys.argv) > 1:
        image_path = sys.argv[1]

    print(f"🔍 Processing image: {image_path}")

    try:
        ocr_text = detect_text(image_path)
    except Exception as e:
        print(f"❌ Failed to extract text from image: {e}")
        return

    try:
        extracted_data = chain.invoke({"ocr_text": ocr_text})
        print("\n✅ Final Structured Output:\n")

        # Convert to dict and pretty print
        if hasattr(extracted_data, 'model_dump'):
            # Pydantic v2
            result_dict = extracted_data.model_dump()
        elif hasattr(extracted_data, 'dict'):
            # Pydantic v1
            result_dict = extracted_data.dict()
        else:
            # Fallback
            result_dict = extracted_data

        print(json.dumps(result_dict, indent=2, ensure_ascii=False))

        # Save to file
        output_file = "extracted_insurance_data.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result_dict, f, indent=2, ensure_ascii=False)
        print(f"\n💾 Data saved to: {output_file}")

    except Exception as e:
        print("❌ Parsing failed:", e)
        print("❌ Error type:", type(e).__name__)

        # Try to extract data manually as fallback
        print("\n🔄 Attempting manual extraction as fallback...")
        manual_data = extract_manual_fallback(ocr_text)
        print(json.dumps(manual_data, indent=2, ensure_ascii=False))


if __name__ == "__main__":
    main()