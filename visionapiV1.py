import os
import json
from typing import Optional
from pydantic import BaseModel, <PERSON>
from google.cloud import vision
from langchain.prompts import PromptTemplate
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain.output_parsers import StructuredOutputParser
from langchain.schema.runnable import RunnableSequence

# ----------------------------------
# 🔐 API Keys & Auth
# ----------------------------------

# Google Vision API - Service Account Key
os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = "/Users/<USER>/Documents/hackathon/InsuranceOCR/ca-healthcareinsightsplatform-8509ba7b2e54.json"

# Gemini API Key
os.environ["GOOGLE_API_KEY"] = "AIzaSyCGa46rck1Z1ZLB8gf2bevoAuSACOwogl8"  # 👈 Replace with your Gemini API key


# ----------------------------------
# 📄 OCR with Google Vision API
# ----------------------------------

def detect_text(path):
    """Detects text in the file located in `path` using Google Vision API."""
    client = vision.ImageAnnotatorClient()

    with open(path, "rb") as image_file:
        content = image_file.read()

    image = vision.Image(content=content)

    response = client.document_text_detection(image=image)

    if response.error.message:
        raise Exception(f"Vision API Error: {response.error.message}")

    full_text = response.full_text_annotation.text
    print("\n✅ Full OCR Text:\n")
    print(full_text)

    return full_text


# ----------------------------------
# ✅ Define Pydantic Schema
# ----------------------------------

class InsuranceCardData(BaseModel):
    member_id: Optional[str] = Field(None, description="Member ID Number")
    member_name: Optional[str] = Field(None, description="Name of the member")
    group_number: Optional[str] = Field(None, description="Group number on the card")
    pcp_name: Optional[str] = Field(None, description="Primary Care Provider Name")
    pcp_phone: Optional[str] = Field(None, description="Primary Care Provider Phone Number")
    plan_type: Optional[str] = Field(None, description="Insurance plan type (e.g., PPO, HMO)")
    copay_primary: Optional[str] = Field(None, description="Co-pay for primary care visits")
    copay_specialist: Optional[str] = Field(None, description="Co-pay for specialist visits")
    copay_emergency: Optional[str] = Field(None, description="Co-pay for emergency care")
    copay_urgent: Optional[str] = Field(None, description="Co-pay for urgent care")
    rx_info: Optional[str] = Field(None, description="Prescription Drug Plan Information")
    website: Optional[str] = Field(None, description="Health plan website")
    in_network_deductible: Optional[str] = Field(None, description="In-network deductible and coinsurance")
    oon_deductible: Optional[str] = Field(None, description="Out-of-network deductible and coinsurance")
    contact_info: Optional[str] = Field(None, description="Plan contact information")


# ----------------------------------
# 🧠 LLM + Prompt + Parser Chain
# ----------------------------------

# Load Gemini LLM
llm = ChatGoogleGenerativeAI(model="models/gemini-pro", temperature=0)

# Create parser from Pydantic schema
parser = StructuredOutputParser.from_automatic_schema(InsuranceCardData)

# Build prompt
prompt = PromptTemplate(
    template="""
You are a helpful assistant that extracts structured insurance card information from OCR text.
Extract the following fields from the OCR text and return them in the correct JSON format:

{format_instructions}

OCR TEXT:
\"\"\"
{ocr_text}
\"\"\"
""",
    input_variables=["ocr_text"],
    partial_variables={"format_instructions": parser.get_format_instructions()},
)

# Combine all into a runnable chain
chain: RunnableSequence = prompt | llm | parser


# ----------------------------------
# 🚀 Run It All Together
# ----------------------------------

if __name__ == "__main__":
    image_path = "/Users/<USER>/Downloads/Insurance Card.v1i.yolov7pytorch/train/images/IMG_2398_jpeg.rf.b19629dc9f57b55de251c710eec7f368.jpg"

    ocr_text = detect_text(image_path)

    try:
        extracted_data = chain.invoke({"ocr_text": ocr_text})
        print("\n✅ Final Structured Output:\n")
        print(extracted_data.json(indent=2))
    except Exception as e:
        print("❌ Parsing failed:", e)