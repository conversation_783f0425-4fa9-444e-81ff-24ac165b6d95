# OAuth Troubleshooting Guide

## ❌ Error: "redirect_uri_mismatch"

If you see this error:
```
Error 400: redirect_uri_mismatch
You can't sign in to this app because it doesn't comply with Google's OAuth 2.0 policy.
Request details: redirect_uri=http://localhost:XXXX/
```

### 🔧 **Step-by-Step Fix:**

1. **Open Google Cloud Console:**
   - Go to [https://console.cloud.google.com/](https://console.cloud.google.com/)
   - Select your project

2. **Navigate to Credentials:**
   - Click "APIs & Services" in the left menu
   - Click "Credentials"

3. **Edit OAuth 2.0 Client:**
   - Find your OAuth 2.0 Client ID in the list
   - Click the pencil/edit icon

4. **Add Redirect URIs:**
   - Scroll down to "Authorized redirect URIs"
   - Click "ADD URI" and add each of these:
     ```
     http://localhost:8080/
     http://localhost:8081/
     http://localhost:8082/
     http://localhost:8083/
     http://localhost:8084/
     ```

5. **Save Changes:**
   - Click "SAVE" at the bottom

6. **Wait a Moment:**
   - Google may take a few seconds to propagate the changes

7. **Try Again:**
   - Run the setup script again: `python setup_calendar_auth.py`

### 📸 **Visual Guide:**

```
Google Cloud Console → APIs & Services → Credentials → OAuth 2.0 Client IDs → Edit

In the "Authorized redirect URIs" section:
┌─────────────────────────────────────────┐
│ Authorized redirect URIs                │
├─────────────────────────────────────────┤
│ http://localhost:8080/          [DELETE]│
│ http://localhost:8081/          [DELETE]│
│ http://localhost:8082/          [DELETE]│
│ http://localhost:8083/          [DELETE]│
│ http://localhost:8084/          [DELETE]│
│                           [+ ADD URI]   │
└─────────────────────────────────────────┘
```

### 🚨 **Common Mistakes:**

❌ **Don't forget the trailing slash:** Use `http://localhost:8080/` not `http://localhost:8080`
❌ **Don't use HTTPS:** Use `http://` not `https://`
❌ **Don't use different ports:** Stick to the ports listed above

### 🔍 **Alternative Method - Desktop Application:**

If you continue having issues, you can create a "Desktop Application" OAuth client instead:

1. In Google Cloud Console → Credentials
2. Click "CREATE CREDENTIALS" → "OAuth client ID"
3. Choose "Desktop application"
4. Give it a name like "Calendar API Desktop"
5. Download the JSON file and replace your `credentials.json`

Desktop applications don't require redirect URI configuration.

### 🧪 **Test Your Setup:**

After making changes, test with:
```bash
python setup_calendar_auth.py
```

You should see:
```
✅ credentials.json found!
🔐 Starting OAuth authentication flow...
Trying to start local server on port 8080...
✅ Authentication successful!
✅ Token saved to token.pickle
🧪 Testing Calendar API connection...
✅ Connection successful! Found X calendars:
```

### 🆘 **Still Having Issues?**

1. **Check your project has Calendar API enabled:**
   - Go to "APIs & Services" → "Library"
   - Search for "Google Calendar API"
   - Make sure it's enabled

2. **Verify your OAuth consent screen:**
   - Go to "APIs & Services" → "OAuth consent screen"
   - Make sure it's configured properly

3. **Try deleting and recreating credentials:**
   - Delete the current OAuth 2.0 Client ID
   - Create a new one as "Desktop application"
   - Download new credentials.json

4. **Clear existing tokens:**
   ```bash
   rm token.pickle
   python setup_calendar_auth.py
   ```

### 📞 **Get Help:**

If you're still stuck:
1. Check the error message carefully
2. Verify all redirect URIs are added correctly
3. Make sure you're using the right credentials.json file
4. Try the desktop application method above

---

**Happy authenticating! 🔐✨** 