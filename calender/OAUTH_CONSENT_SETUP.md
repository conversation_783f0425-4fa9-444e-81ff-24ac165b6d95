# OAuth Consent Screen Setup Guide

## 🚨 Error: "Access blocked: Voice AI Agent has not completed the Google verification process"

This error occurs when your OAuth consent screen is in "Testing" mode and you haven't added yourself as a test user.

## 🔧 **Quick Fix: Add Test User**

### Step 1: Go to OAuth Consent Screen
1. Open [Google Cloud Console](https://console.cloud.google.com/)
2. Select your project
3. Navigate to **"APIs & Services"** → **"OAuth consent screen"**

### Step 2: Add Your Email as Test User
1. Scroll down to the **"Test users"** section
2. Click **"+ ADD USERS"**
3. Enter your email: `<EMAIL>`
4. Click **"SAVE"**

### Step 3: Test Again
```bash
python setup_calendar_auth.py
```

## 🏗️ **Complete OAuth Consent Screen Setup**

If you haven't set up your OAuth consent screen yet, follow these steps:

### 1. **Choose User Type**

**External (Recommended for personal use):**
- ✅ Works with any Google account
- ❌ Requires adding test users manually
- ❌ Limited to 100 test users

**Internal (Google Workspace only):**
- ✅ All users in your organization can access
- ❌ Only works if you have Google Workspace
- ❌ Requires domain ownership

### 2. **Fill Required Information**

**App Information:**
```
App name: Calendar API App
User support email: <EMAIL>
Developer contact: <EMAIL>
```

**App Domain (Optional but recommended):**
```
Application home page: https://github.com/yourusername/calendar-api
Application privacy policy: https://github.com/yourusername/calendar-api/privacy
Application terms of service: https://github.com/yourusername/calendar-api/terms
```

**Authorized Domains (Optional):**
- Leave empty for local development

### 3. **Configure Scopes**

The Calendar API requires these scopes:
- `https://www.googleapis.com/auth/calendar.readonly`
- `https://www.googleapis.com/auth/calendar.events`

**To add scopes:**
1. Click **"ADD OR REMOVE SCOPES"**
2. Search for "Google Calendar API"
3. Select both scopes above
4. Click **"UPDATE"**

### 4. **Add Test Users**

In the "Test users" section, add:
- Your email address
- Any other users who need access
- Up to 100 test users allowed

### 5. **Publishing Status**

**Testing Mode (Default):**
- ✅ Good for development
- ❌ Only test users can access
- ❌ Tokens expire every 7 days

**In Production:**
- ✅ Anyone can use the app
- ❌ Requires Google verification process
- ❌ Can take weeks to get approved

## 📋 **Complete Setup Checklist**

- [ ] OAuth consent screen configured
- [ ] User type selected (External recommended)
- [ ] App name and contact emails filled
- [ ] Calendar API scopes added
- [ ] Your email added as test user
- [ ] OAuth 2.0 Client ID created
- [ ] Redirect URIs configured
- [ ] credentials.json downloaded

## 🔄 **Alternative Solutions**

### Option 1: Use Desktop Application Type
Instead of "Web application", create a "Desktop application" OAuth client:

1. Go to **"APIs & Services"** → **"Credentials"**
2. Click **"CREATE CREDENTIALS"** → **"OAuth client ID"**
3. Choose **"Desktop application"**
4. Name it "Calendar API Desktop"
5. Download the JSON file as `credentials.json`

Desktop applications have fewer restrictions.

### Option 2: Google Workspace Internal App
If you have Google Workspace:

1. Set User Type to **"Internal"**
2. All users in your domain can access automatically
3. No need to add individual test users

### Option 3: Service Account (Advanced)
For server-to-server applications:

1. Create a Service Account instead of OAuth client
2. Download the service account key
3. No user consent required
4. Limited to accessing calendars the service account owns

## 🧪 **Testing Your Setup**

After configuration, test with:

```bash
# Clear any existing tokens
rm token.pickle

# Run setup
python setup_calendar_auth.py
```

**Expected output:**
```
✅ credentials.json found!
🔐 Starting OAuth authentication flow...
Trying to start local server on port 8080...
✅ Authentication successful!
✅ Token saved to token.pickle
🧪 Testing Calendar API connection...
✅ Connection successful! Found X calendars:
```

## 🆘 **Troubleshooting Common Issues**

### Issue: "App domain verification required"
**Solution:** Leave app domain fields empty for local development

### Issue: "Invalid scope"
**Solution:** Make sure you added the correct Calendar API scopes

### Issue: "Unauthorized client"
**Solution:** Check that your credentials.json matches your OAuth client

### Issue: "Token expired"
**Solution:** In testing mode, tokens expire every 7 days. Re-authenticate:
```bash
rm token.pickle
python setup_calendar_auth.py
```

## 📞 **Need Help?**

If you're still having issues:

1. **Double-check test users:** Make sure your email is in the test users list
2. **Verify scopes:** Ensure Calendar API scopes are added
3. **Check credentials:** Make sure credentials.json is from the right OAuth client
4. **Try desktop app:** Create a desktop application OAuth client instead

---

**Happy authenticating! 🔐✨** 