"""
Test script for Google Calendar API
Verifies all major functionality works correctly.
"""

import requests
import json
import time
from datetime import datetime, timedelta
from typing import Dict, Any

BASE_URL = "http://localhost:8000"

class CalendarAPITester:
    """Test suite for Calendar API"""
    
    def __init__(self):
        self.base_url = BASE_URL
        self.test_event_id = None
        self.passed_tests = 0
        self.failed_tests = 0
    
    def log_test(self, test_name: str, success: bool, message: str = ""):
        """Log test results"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status}: {test_name}")
        if message:
            print(f"    {message}")
        
        if success:
            self.passed_tests += 1
        else:
            self.failed_tests += 1
    
    def test_server_connection(self) -> bool:
        """Test if server is running"""
        try:
            response = requests.get(f"{self.base_url}/", timeout=5)
            success = response.status_code == 200
            
            if success:
                data = response.json()
                message = f"Server running - {data.get('message', 'Unknown')}"
            else:
                message = f"Server returned status {response.status_code}"
            
            self.log_test("Server Connection", success, message)
            return success
        
        except Exception as e:
            self.log_test("Server Connection", False, f"Connection failed: {e}")
            return False
    
    def test_get_calendars(self) -> bool:
        """Test getting calendar list"""
        try:
            response = requests.get(f"{self.base_url}/calendars", timeout=10)
            success = response.status_code == 200
            
            if success:
                calendars = response.json()
                message = f"Found {len(calendars)} calendars"
            else:
                message = f"Request failed: {response.status_code} - {response.text}"
            
            self.log_test("Get Calendars", success, message)
            return success
        
        except Exception as e:
            self.log_test("Get Calendars", False, f"Request failed: {e}")
            return False
    
    def test_get_schedule(self) -> bool:
        """Test getting schedule"""
        try:
            schedule_data = {
                "start_date": datetime.now().isoformat(),
                "end_date": (datetime.now() + timedelta(days=7)).isoformat(),
                "timezone": "UTC",
                "max_results": 10
            }
            
            response = requests.post(
                f"{self.base_url}/schedule", 
                json=schedule_data,
                timeout=10
            )
            
            success = response.status_code == 200
            
            if success:
                events = response.json()
                message = f"Retrieved {len(events)} events"
            else:
                message = f"Request failed: {response.status_code} - {response.text}"
            
            self.log_test("Get Schedule", success, message)
            return success
        
        except Exception as e:
            self.log_test("Get Schedule", False, f"Request failed: {e}")
            return False
    
    def test_create_event(self) -> bool:
        """Test creating a calendar event"""
        try:
            # Create event for tomorrow at 3 PM
            tomorrow = datetime.now() + timedelta(days=1)
            start_time = tomorrow.replace(hour=15, minute=0, second=0, microsecond=0)
            end_time = start_time + timedelta(minutes=30)
            
            event_data = {
                "summary": "API Test Event - Please Delete",
                "description": "This is a test event created by the Calendar API test suite",
                "start_datetime": start_time.isoformat(),
                "end_datetime": end_time.isoformat(),
                "timezone": "UTC",
                "location": "Test Location"
            }
            
            response = requests.post(
                f"{self.base_url}/events",
                json=event_data,
                timeout=10
            )
            
            success = response.status_code == 200
            
            if success:
                event = response.json()
                self.test_event_id = event['id']  # Save for cleanup
                message = f"Created event: {event['summary']} (ID: {event['id'][:10]}...)"
            else:
                message = f"Request failed: {response.status_code} - {response.text}"
            
            self.log_test("Create Event", success, message)
            return success
        
        except Exception as e:
            self.log_test("Create Event", False, f"Request failed: {e}")
            return False
    
    def test_update_event(self) -> bool:
        """Test updating a calendar event"""
        if not self.test_event_id:
            self.log_test("Update Event", False, "No test event ID available")
            return False
        
        try:
            # Update the test event
            tomorrow = datetime.now() + timedelta(days=1)
            start_time = tomorrow.replace(hour=16, minute=0, second=0, microsecond=0)  # Changed time
            end_time = start_time + timedelta(minutes=45)  # Changed duration
            
            updated_data = {
                "summary": "API Test Event - UPDATED",
                "description": "This test event has been updated",
                "start_datetime": start_time.isoformat(),
                "end_datetime": end_time.isoformat(),
                "timezone": "UTC",
                "location": "Updated Test Location"
            }
            
            response = requests.put(
                f"{self.base_url}/events/{self.test_event_id}",
                json=updated_data,
                timeout=10
            )
            
            success = response.status_code == 200
            
            if success:
                event = response.json()
                message = f"Updated event: {event['summary']}"
            else:
                message = f"Request failed: {response.status_code} - {response.text}"
            
            self.log_test("Update Event", success, message)
            return success
        
        except Exception as e:
            self.log_test("Update Event", False, f"Request failed: {e}")
            return False
    
    def test_find_free_time(self) -> bool:
        """Test finding free time slots"""
        try:
            params = {
                "duration_minutes": 30,
                "start_date": datetime.now().isoformat(),
                "end_date": (datetime.now() + timedelta(days=2)).isoformat(),
                "timezone": "UTC"
            }
            
            response = requests.get(
                f"{self.base_url}/free-time",
                params=params,
                timeout=10
            )
            
            success = response.status_code == 200
            
            if success:
                result = response.json()
                free_slots = result.get("free_slots", [])
                message = f"Found {len(free_slots)} free time slots"
            else:
                message = f"Request failed: {response.status_code} - {response.text}"
            
            self.log_test("Find Free Time", success, message)
            return success
        
        except Exception as e:
            self.log_test("Find Free Time", False, f"Request failed: {e}")
            return False
    
    def test_delete_event(self) -> bool:
        """Test deleting a calendar event"""
        if not self.test_event_id:
            self.log_test("Delete Event", False, "No test event ID available")
            return False
        
        try:
            response = requests.delete(
                f"{self.base_url}/events/{self.test_event_id}",
                timeout=10
            )
            
            success = response.status_code == 200
            
            if success:
                result = response.json()
                message = f"Deleted event successfully"
                self.test_event_id = None  # Clear the ID
            else:
                message = f"Request failed: {response.status_code} - {response.text}"
            
            self.log_test("Delete Event", success, message)
            return success
        
        except Exception as e:
            self.log_test("Delete Event", False, f"Request failed: {e}")
            return False
    
    def cleanup(self):
        """Clean up any remaining test events"""
        if self.test_event_id:
            print("\n🧹 Cleaning up test event...")
            try:
                requests.delete(f"{self.base_url}/events/{self.test_event_id}", timeout=5)
                print("✅ Test event cleaned up")
            except:
                print("⚠️  Could not clean up test event - you may need to delete it manually")
    
    def run_all_tests(self):
        """Run all tests"""
        print("🧪 Google Calendar API Test Suite")
        print("=" * 40)
        
        # Test server connection first
        if not self.test_server_connection():
            print("\n❌ Server connection failed. Make sure the API server is running:")
            print("   python calendar_api.py")
            return
        
        print("\n🔄 Running API tests...")
        print("-" * 30)
        
        # Run all tests
        tests = [
            self.test_get_calendars,
            self.test_get_schedule,
            self.test_create_event,
            self.test_update_event,
            self.test_find_free_time,
            self.test_delete_event
        ]
        
        for test in tests:
            try:
                test()
                time.sleep(0.5)  # Small delay between tests
            except KeyboardInterrupt:
                print("\n⏹️  Tests interrupted by user")
                break
            except Exception as e:
                print(f"❌ Unexpected error in {test.__name__}: {e}")
                self.failed_tests += 1
        
        # Clean up
        self.cleanup()
        
        # Print summary
        print("\n" + "=" * 40)
        print("📊 Test Results Summary")
        print(f"✅ Passed: {self.passed_tests}")
        print(f"❌ Failed: {self.failed_tests}")
        print(f"📈 Success Rate: {self.passed_tests / (self.passed_tests + self.failed_tests) * 100:.1f}%")
        
        if self.failed_tests == 0:
            print("\n🎉 All tests passed! Your Calendar API is working correctly.")
        else:
            print(f"\n⚠️  {self.failed_tests} test(s) failed. Check the errors above.")
            print("\nCommon issues:")
            print("- Make sure you've run: python setup_calendar_auth.py")
            print("- Ensure credentials.json is present")
            print("- Check that Google Calendar API is enabled")

def main():
    """Main test function"""
    tester = CalendarAPITester()
    
    try:
        tester.run_all_tests()
    except KeyboardInterrupt:
        print("\n👋 Tests cancelled by user")
        tester.cleanup()

if __name__ == "__main__":
    main() 