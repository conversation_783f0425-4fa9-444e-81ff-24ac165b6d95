# Google Calendar API

A comprehensive FastAPI-based service for Google Calendar integration that allows you to read your schedule and book new meetings.

## Features

- 🔐 **OAuth 2.0 Authentication** - Secure Google Calendar access
- 📅 **Schedule Reading** - Fetch events from your calendar
- 📝 **Meeting Creation** - Create new calendar events with attendees
- ✏️ **Event Management** - Update and delete existing events
- 🔍 **Free Time Finding** - Automatically find available time slots
- 📚 **Multiple Calendars** - Support for multiple calendar access
- 🌍 **Timezone Support** - Handle different timezones properly
- 🚀 **REST API** - Easy integration with any application

## Quick Start

### 1. Installation

```bash
# Install dependencies
pip install -r requirements.txt
```

### 2. Google Calendar API Setup

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google Calendar API
4. Go to "Credentials" and create OAuth 2.0 Client IDs
5. Download the JSON file and rename it to `credentials.json`
6. Place it in the project directory

### 3. Authentication Setup

```bash
# Run the setup script to authenticate
python setup_calendar_auth.py
```

This will:
- Check for your credentials file
- Open a browser for OAuth authentication
- Save your authentication token
- Test the connection

### 4. Start the API Server

```bash
# Start the FastAPI server
python calendar_api.py
```

The API will be available at `http://localhost:8000`

### 5. Try the Examples

```bash
# Run example usage scripts
python calendar_examples.py
```

## API Endpoints

### Base URL: `http://localhost:8000`

### 1. Get API Information
```http
GET /
```

### 2. List Calendars
```http
GET /calendars
```

### 3. Get Schedule
```http
POST /schedule
```

**Request Body:**
```json
{
  "start_date": "2023-12-01T00:00:00",
  "end_date": "2023-12-08T00:00:00",
  "timezone": "America/New_York",
  "max_results": 50
}
```

### 4. Create Event/Meeting
```http
POST /events
```

**Request Body:**
```json
{
  "summary": "Team Meeting",
  "description": "Weekly team sync",
  "start_datetime": "2023-12-01T14:00:00",
  "end_datetime": "2023-12-01T15:00:00",
  "timezone": "America/New_York",
  "location": "Conference Room A",
  "attendees": ["<EMAIL>", "<EMAIL>"]
}
```

### 5. Update Event
```http
PUT /events/{event_id}
```

### 6. Delete Event
```http
DELETE /events/{event_id}
```

### 7. Find Free Time
```http
GET /free-time?duration_minutes=60&start_date=2023-12-01T00:00:00&timezone=UTC
```

## Usage Examples

### Python Examples

#### Get Your Schedule
```python
import requests
from datetime import datetime, timedelta

# Get next week's schedule
schedule_data = {
    "start_date": datetime.now().isoformat(),
    "end_date": (datetime.now() + timedelta(days=7)).isoformat(),
    "timezone": "America/New_York",
    "max_results": 20
}

response = requests.post("http://localhost:8000/schedule", json=schedule_data)
events = response.json()

for event in events:
    print(f"{event['summary']} - {event['start']['dateTime']}")
```

#### Create a Meeting
```python
import requests
from datetime import datetime, timedelta

# Schedule a meeting for tomorrow at 2 PM
tomorrow = datetime.now() + timedelta(days=1)
start_time = tomorrow.replace(hour=14, minute=0, second=0, microsecond=0)
end_time = start_time + timedelta(hours=1)

meeting_data = {
    "summary": "Project Review",
    "description": "Monthly project review meeting",
    "start_datetime": start_time.isoformat(),
    "end_datetime": end_time.isoformat(),
    "timezone": "America/New_York",
    "location": "Zoom Meeting",
    "attendees": ["<EMAIL>"]
}

response = requests.post("http://localhost:8000/events", json=meeting_data)
event = response.json()
print(f"Meeting created: {event['html_link']}")
```

#### Find Available Time
```python
import requests
from datetime import datetime, timedelta

# Find 1-hour slots in the next 3 days
params = {
    "duration_minutes": 60,
    "start_date": datetime.now().isoformat(),
    "end_date": (datetime.now() + timedelta(days=3)).isoformat(),
    "timezone": "America/New_York"
}

response = requests.get("http://localhost:8000/free-time", params=params)
result = response.json()

print(f"Found {len(result['free_slots'])} available time slots:")
for slot in result['free_slots'][:5]:  # Show first 5
    print(f"  {slot['start']} - {slot['end']}")
```

### cURL Examples

#### Get Schedule
```bash
curl -X POST "http://localhost:8000/schedule" \
  -H "Content-Type: application/json" \
  -d '{
    "start_date": "2023-12-01T00:00:00",
    "end_date": "2023-12-08T00:00:00",
    "timezone": "America/New_York"
  }'
```

#### Create Meeting
```bash
curl -X POST "http://localhost:8000/events" \
  -H "Content-Type: application/json" \
  -d '{
    "summary": "Team Standup",
    "start_datetime": "2023-12-01T09:00:00",
    "end_datetime": "2023-12-01T09:30:00",
    "timezone": "America/New_York",
    "attendees": ["<EMAIL>"]
  }'
```

#### Find Free Time
```bash
curl "http://localhost:8000/free-time?duration_minutes=30&timezone=America/New_York"
```

## Configuration

### Environment Variables

You can customize the API using environment variables:

```bash
# Server configuration
export CALENDAR_HOST="0.0.0.0"
export CALENDAR_PORT="8000"

# Authentication files
export CREDENTIALS_FILE="credentials.json"
export TOKEN_FILE="token.pickle"
```

### Timezone Support

The API supports all standard timezone names. Common examples:
- `UTC`
- `America/New_York`
- `America/Los_Angeles`
- `Europe/London`
- `Asia/Tokyo`

## Error Handling

The API includes comprehensive error handling:

- **400 Bad Request** - Invalid request data or parameters
- **401 Unauthorized** - Authentication issues
- **404 Not Found** - Event or calendar not found
- **500 Internal Server Error** - Server-side errors

Example error response:
```json
{
  "detail": "Error creating event: Invalid datetime format"
}
```

## Security

- Uses OAuth 2.0 for secure Google Calendar access
- Credentials are stored locally and encrypted
- No sensitive data is logged
- Supports token refresh automatically

## Advanced Features

### Multiple Calendar Support

You can specify which calendar to use by adding the `calendar_id` parameter:

```python
# Use a specific calendar
response = requests.post(
    "http://localhost:8000/schedule?calendar_id=<EMAIL>",
    json=schedule_data
)
```

### Batch Operations

For multiple operations, you can make concurrent requests:

```python
import asyncio
import aiohttp

async def create_multiple_meetings(meetings):
    async with aiohttp.ClientSession() as session:
        tasks = []
        for meeting in meetings:
            task = session.post("http://localhost:8000/events", json=meeting)
            tasks.append(task)
        
        responses = await asyncio.gather(*tasks)
        return responses
```

## Troubleshooting

### Common Issues

1. **"credentials.json not found"**
   - Download credentials from Google Cloud Console
   - Ensure file is named exactly `credentials.json`

2. **"Authentication failed"**
   - Delete `token.pickle` and re-run setup
   - Check that Google Calendar API is enabled

3. **"Calendar service not initialized"**
   - Ensure authentication completed successfully
   - Check server logs for detailed error messages

4. **"Invalid datetime format"**
   - Use ISO format: `2023-12-01T14:00:00`
   - Include timezone information when needed

### Debug Mode

Run the server in debug mode for detailed logging:

```bash
# Enable debug logging
export PYTHONPATH="."
python -c "
import logging
logging.basicConfig(level=logging.DEBUG)
from calendar_api import app
import uvicorn
uvicorn.run(app, host='0.0.0.0', port=8000, log_level='debug')
"
```

## API Documentation

When the server is running, you can access:
- **Interactive API docs**: http://localhost:8000/docs
- **ReDoc documentation**: http://localhost:8000/redoc
- **OpenAPI schema**: http://localhost:8000/openapi.json

## Development

### Project Structure
```
├── calendar_api.py          # Main API server
├── setup_calendar_auth.py   # Authentication setup
├── calendar_examples.py     # Usage examples
├── requirements.txt         # Dependencies
├── credentials.json         # Google API credentials (you provide)
├── token.pickle            # Authentication token (auto-generated)
└── CALENDAR_API_README.md  # This documentation
```

### Running Tests

```bash
# Test authentication
python setup_calendar_auth.py

# Test API endpoints
python calendar_examples.py

# Manual testing with curl
curl http://localhost:8000/
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if needed
5. Submit a pull request

## License

This project is open source and available under the MIT License.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review the API documentation at `/docs`
3. Run the setup script for authentication issues
4. Check Google Calendar API quotas and limits

---

**Happy scheduling! 📅✨** 