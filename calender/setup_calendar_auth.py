"""
Google Calendar API Setup Script
Helps users set up authentication for the Calendar API.
"""

import os
import json
import pickle
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow

SCOPES = [
    'https://www.googleapis.com/auth/calendar.readonly',
    'https://www.googleapis.com/auth/calendar.events'
]

def setup_credentials():
    """Set up Google Calendar API credentials"""
    
    print("Google Calendar API Setup")
    print("=" * 30)
    
    # Check if credentials.json exists
    if not os.path.exists('credentials.json'):
        print("\n❌ credentials.json not found!")
        print("\nTo get your credentials.json file:")
        print("1. Go to https://console.cloud.google.com/")
        print("2. Create a new project or select an existing one")
        print("3. Enable the Google Calendar API")
        print("4. Go to 'Credentials' and create OAuth 2.0 Client IDs")
        print("5. Download the JSON file and rename it to 'credentials.json'")
        print("6. Place it in the same directory as this script")
        return False
    
    print("✅ credentials.json found!")
    
    # Check if token already exists
    if os.path.exists('token.pickle'):
        print("✅ Existing authentication token found!")
        
        # Load and check token validity
        with open('token.pickle', 'rb') as token:
            creds = pickle.load(token)
        
        if creds and creds.valid:
            print("✅ Token is valid and ready to use!")
            return True
        elif creds and creds.expired and creds.refresh_token:
            print("🔄 Token expired, attempting to refresh...")
            try:
                creds.refresh(Request())
                with open('token.pickle', 'wb') as token:
                    pickle.dump(creds, token)
                print("✅ Token refreshed successfully!")
                return True
            except Exception as e:
                print(f"❌ Error refreshing token: {e}")
                print("Will need to re-authenticate...")
    
    # Perform OAuth flow
    print("\n🔐 Starting OAuth authentication flow...")
    print("This will open a browser window for authentication.")
    
    # Try multiple ports in case one is in use
    ports_to_try = [8080, 8081, 8082, 8083, 8084]
    
    for port in ports_to_try:
        try:
            print(f"Trying to start local server on port {port}...")
            flow = InstalledAppFlow.from_client_secrets_file(
                'credentials.json', SCOPES
            )
            creds = flow.run_local_server(port=port)
            
            # Save credentials
            with open('token.pickle', 'wb') as token:
                pickle.dump(creds, token)
            
            print("✅ Authentication successful!")
            print("✅ Token saved to token.pickle")
            return True
        
        except Exception as e:
            error_msg = str(e).lower()
            if "redirect_uri_mismatch" in error_msg:
                print(f"❌ OAuth redirect URI error on port {port}")
                print("\n🔧 To fix this issue:")
                print("1. Go to https://console.cloud.google.com/")
                print("2. Navigate to 'APIs & Services' → 'Credentials'")
                print("3. Edit your OAuth 2.0 Client ID")
                print("4. In 'Authorized redirect URIs', add:")
                for p in ports_to_try:
                    print(f"   http://localhost:{p}/")
                print("5. Save and try again")
                return False
            elif "access_denied" in error_msg or "verification process" in error_msg:
                print(f"❌ OAuth access denied - App not verified")
                print("\n🔧 To fix this issue:")
                print("1. Go to https://console.cloud.google.com/")
                print("2. Navigate to 'APIs & Services' → 'OAuth consent screen'")
                print("3. Scroll down to 'Test users' section")
                print("4. Click 'ADD USERS' and add your email address")
                print("5. Click 'SAVE' and try again")
                print("\nAlternatively:")
                print("- If using Google Workspace, set User Type to 'Internal'")
                print("- Or publish the app (requires Google verification)")
                return False
            elif "address already in use" in error_msg or "port" in error_msg:
                print(f"Port {port} is in use, trying next port...")
                continue
            else:
                print(f"❌ Authentication failed on port {port}: {e}")
                if port == ports_to_try[-1]:  # Last port
                    return False
                continue
    
    print("❌ All ports failed. Please check your network and Google Cloud Console settings.")
    return False

def test_connection():
    """Test the calendar API connection"""
    
    print("\n🧪 Testing Calendar API connection...")
    
    try:
        from googleapiclient.discovery import build
        
        # Load credentials
        with open('token.pickle', 'rb') as token:
            creds = pickle.load(token)
        
        # Build service
        service = build('calendar', 'v3', credentials=creds)
        
        # Test by getting calendar list
        calendar_list = service.calendarList().list().execute()
        calendars = calendar_list.get('items', [])
        
        print(f"✅ Connection successful! Found {len(calendars)} calendars:")
        for i, calendar in enumerate(calendars[:3], 1):  # Show first 3
            print(f"   {i}. {calendar.get('summary', 'Unknown')}")
        
        if len(calendars) > 3:
            print(f"   ... and {len(calendars) - 3} more")
        
        return True
    
    except Exception as e:
        print(f"❌ Connection test failed: {e}")
        return False

def main():
    """Main setup function"""
    
    print("🚀 Setting up Google Calendar API...\n")
    
    # Setup credentials
    if not setup_credentials():
        print("\n❌ Setup failed. Please check the instructions above.")
        return
    
    # Test connection
    if not test_connection():
        print("\n❌ Connection test failed.")
        return
    
    print("\n🎉 Setup completed successfully!")
    print("\nYou can now run the Calendar API:")
    print("   python calendar_api.py")
    print("\nOr install dependencies first:")
    print("   pip install -r requirements.txt")
    print("   python calendar_api.py")

if __name__ == "__main__":
    main() 