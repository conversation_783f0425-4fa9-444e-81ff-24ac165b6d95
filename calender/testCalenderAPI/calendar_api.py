"""
Google Calendar API Service
Provides comprehensive calendar operations including reading schedules and booking meetings.
"""

import os
import json
import pickle
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
import pytz
from dateutil import parser

from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from fastapi import FastAPI, HTTPException, Depends
from fastapi.security import HTTPBearer
from pydantic import BaseModel, Field
import uvicorn


# OAuth 2.0 scopes for Google Calendar API
SCOPES = [
    'https://www.googleapis.com/auth/calendar.readonly',
    'https://www.googleapis.com/auth/calendar.events'
]

# Pydantic models for API requests/responses
class EventCreate(BaseModel):
    summary: str = Field(..., description="Event title/summary")
    description: Optional[str] = Field(None, description="Event description")
    start_datetime: str = Field(..., description="Start datetime in ISO format (e.g., '2023-12-01T10:00:00')")
    end_datetime: str = Field(..., description="End datetime in ISO format (e.g., '2023-12-01T11:00:00')")
    timezone: str = Field("UTC", description="Timezone (e.g., 'America/New_York')")
    attendees: Optional[List[str]] = Field(None, description="List of attendee email addresses")
    location: Optional[str] = Field(None, description="Event location")

class EventResponse(BaseModel):
    id: str
    summary: str
    description: Optional[str]
    start: Dict[str, Any]
    end: Dict[str, Any]
    location: Optional[str]
    attendees: Optional[List[Dict[str, str]]]
    html_link: str

class ScheduleQuery(BaseModel):
    start_date: Optional[str] = Field(None, description="Start date in ISO format (default: today)")
    end_date: Optional[str] = Field(None, description="End date in ISO format (default: 7 days from now)")
    timezone: str = Field("UTC", description="Timezone for the query")
    max_results: int = Field(50, description="Maximum number of events to return")

class CalendarService:
    """Google Calendar API service wrapper"""
    
    def __init__(self, credentials_file: str = 'credentials.json', token_file: str = 'token.pickle'):
        self.credentials_file = credentials_file
        self.token_file = token_file
        self.service = None
        self._authenticate()
    
    def _authenticate(self):
        """Authenticate with Google Calendar API"""
        creds = None
        
        # Load existing token
        if os.path.exists(self.token_file):
            with open(self.token_file, 'rb') as token:
                creds = pickle.load(token)
        
        # If there are no valid credentials, request authorization
        if not creds or not creds.valid:
            if creds and creds.expired and creds.refresh_token:
                try:
                    creds.refresh(Request())
                except Exception as e:
                    print(f"Error refreshing credentials: {e}")
                    creds = None
            
            if not creds:
                if not os.path.exists(self.credentials_file):
                    raise FileNotFoundError(
                        f"Credentials file '{self.credentials_file}' not found. "
                        "Please download it from Google Cloud Console."
                    )
                
                flow = InstalledAppFlow.from_client_secrets_file(
                    self.credentials_file, SCOPES
                )
                # Try multiple ports for consistency with setup script
                ports_to_try = [8080, 8081, 8082, 8083, 8084]
                creds = None
                
                for port in ports_to_try:
                    try:
                        creds = flow.run_local_server(port=port)
                        break
                    except Exception as e:
                        if "redirect_uri_mismatch" in str(e).lower():
                            raise Exception(
                                f"OAuth redirect URI mismatch. Please add http://localhost:{port}/ "
                                "to your Google Cloud Console OAuth 2.0 client authorized redirect URIs."
                            )
                        continue
                
                if not creds:
                    raise Exception("Could not authenticate on any available port")
            
            # Save credentials for next run
            with open(self.token_file, 'wb') as token:
                pickle.dump(creds, token)
        
        self.service = build('calendar', 'v3', credentials=creds)
    
    def get_calendar_list(self) -> List[Dict[str, Any]]:
        """Get list of user's calendars"""
        try:
            calendar_list = self.service.calendarList().list().execute()
            return calendar_list.get('items', [])
        except HttpError as error:
            raise HTTPException(status_code=400, detail=f"Error fetching calendars: {error}")
    
    def get_events(self, 
                   calendar_id: str = 'primary',
                   start_time: Optional[datetime] = None,
                   end_time: Optional[datetime] = None,
                   max_results: int = 50,
                   timezone: str = 'UTC') -> List[Dict[str, Any]]:
        """Get events from calendar within specified time range"""
        
        if start_time is None:
            start_time = datetime.now(pytz.timezone(timezone))
        if end_time is None:
            end_time = start_time + timedelta(days=7)
        
        # Convert to RFC3339 format
        time_min = start_time.isoformat()
        time_max = end_time.isoformat()
        
        try:
            events_result = self.service.events().list(
                calendarId=calendar_id,
                timeMin=time_min,
                timeMax=time_max,
                maxResults=max_results,
                singleEvents=True,
                orderBy='startTime'
            ).execute()
            
            return events_result.get('items', [])
        
        except HttpError as error:
            raise HTTPException(status_code=400, detail=f"Error fetching events: {error}")
    
    def create_event(self, event_data: EventCreate, calendar_id: str = 'primary') -> Dict[str, Any]:
        """Create a new calendar event"""
        
        try:
            # Parse datetime strings
            start_dt = parser.isoparse(event_data.start_datetime)
            end_dt = parser.isoparse(event_data.end_datetime)
            
            # Ensure timezone awareness
            tz = pytz.timezone(event_data.timezone)
            if start_dt.tzinfo is None:
                start_dt = tz.localize(start_dt)
            if end_dt.tzinfo is None:
                end_dt = tz.localize(end_dt)
            
            # Build event object
            event = {
                'summary': event_data.summary,
                'start': {
                    'dateTime': start_dt.isoformat(),
                    'timeZone': event_data.timezone,
                },
                'end': {
                    'dateTime': end_dt.isoformat(),
                    'timeZone': event_data.timezone,
                },
            }
            
            # Add optional fields
            if event_data.description:
                event['description'] = event_data.description
            
            if event_data.location:
                event['location'] = event_data.location
            
            if event_data.attendees:
                event['attendees'] = [{'email': email} for email in event_data.attendees]
            
            # Create the event
            created_event = self.service.events().insert(
                calendarId=calendar_id, 
                body=event
            ).execute()
            
            return created_event
        
        except Exception as error:
            raise HTTPException(status_code=400, detail=f"Error creating event: {error}")
    
    def update_event(self, event_id: str, event_data: EventCreate, calendar_id: str = 'primary') -> Dict[str, Any]:
        """Update an existing calendar event"""
        
        try:
            # Get existing event
            existing_event = self.service.events().get(
                calendarId=calendar_id, 
                eventId=event_id
            ).execute()
            
            # Parse datetime strings
            start_dt = parser.isoparse(event_data.start_datetime)
            end_dt = parser.isoparse(event_data.end_datetime)
            
            # Ensure timezone awareness
            tz = pytz.timezone(event_data.timezone)
            if start_dt.tzinfo is None:
                start_dt = tz.localize(start_dt)
            if end_dt.tzinfo is None:
                end_dt = tz.localize(end_dt)
            
            # Update event fields
            existing_event['summary'] = event_data.summary
            existing_event['start'] = {
                'dateTime': start_dt.isoformat(),
                'timeZone': event_data.timezone,
            }
            existing_event['end'] = {
                'dateTime': end_dt.isoformat(),
                'timeZone': event_data.timezone,
            }
            
            if event_data.description:
                existing_event['description'] = event_data.description
            if event_data.location:
                existing_event['location'] = event_data.location
            if event_data.attendees:
                existing_event['attendees'] = [{'email': email} for email in event_data.attendees]
            
            # Update the event
            updated_event = self.service.events().update(
                calendarId=calendar_id,
                eventId=event_id,
                body=existing_event
            ).execute()
            
            return updated_event
        
        except HttpError as error:
            raise HTTPException(status_code=400, detail=f"Error updating event: {error}")
    
    def delete_event(self, event_id: str, calendar_id: str = 'primary') -> bool:
        """Delete a calendar event"""
        
        try:
            self.service.events().delete(
                calendarId=calendar_id,
                eventId=event_id
            ).execute()
            return True
        
        except HttpError as error:
            raise HTTPException(status_code=400, detail=f"Error deleting event: {error}")
    
    def find_free_time(self, 
                       duration_minutes: int,
                       start_time: Optional[datetime] = None,
                       end_time: Optional[datetime] = None,
                       timezone: str = 'UTC',
                       calendar_id: str = 'primary') -> List[Dict[str, str]]:
        """Find available time slots for scheduling meetings"""
        
        if start_time is None:
            start_time = datetime.now(pytz.timezone(timezone))
        if end_time is None:
            end_time = start_time + timedelta(days=7)
        
        # Get existing events
        events = self.get_events(calendar_id, start_time, end_time, timezone=timezone)
        
        # Extract busy periods
        busy_periods = []
        for event in events:
            if 'dateTime' in event['start'] and 'dateTime' in event['end']:
                event_start = parser.isoparse(event['start']['dateTime'])
                event_end = parser.isoparse(event['end']['dateTime'])
                busy_periods.append((event_start, event_end))
        
        # Sort busy periods by start time
        busy_periods.sort(key=lambda x: x[0])
        
        # Find free slots
        free_slots = []
        current_time = start_time
        duration_delta = timedelta(minutes=duration_minutes)
        
        for busy_start, busy_end in busy_periods:
            # Check if there's a gap before this busy period
            if current_time + duration_delta <= busy_start:
                free_slots.append({
                    'start': current_time.isoformat(),
                    'end': busy_start.isoformat()
                })
            current_time = max(current_time, busy_end)
        
        # Check for time after the last busy period
        if current_time + duration_delta <= end_time:
            free_slots.append({
                'start': current_time.isoformat(),
                'end': end_time.isoformat()
            })
        
        return free_slots


# Initialize FastAPI app
app = FastAPI(
    title="Google Calendar API",
    description="Comprehensive API for Google Calendar operations",
    version="1.0.0"
)

# Initialize calendar service
calendar_service = None

@app.on_event("startup")
async def startup_event():
    """Initialize calendar service on startup"""
    global calendar_service
    try:
        calendar_service = CalendarService()
    except Exception as e:
        print(f"Warning: Could not initialize calendar service: {e}")
        print("Please ensure credentials.json is present and run authentication flow.")

def get_calendar_service():
    """Dependency to get calendar service"""
    if calendar_service is None:
        raise HTTPException(status_code=500, detail="Calendar service not initialized")
    return calendar_service

# API Routes

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Google Calendar API",
        "version": "1.0.0",
        "endpoints": {
            "calendars": "/calendars",
            "events": "/events",
            "create_event": "/events",
            "schedule": "/schedule",
            "free_time": "/free-time"
        }
    }

@app.get("/calendars")
async def get_calendars(service: CalendarService = Depends(get_calendar_service)):
    """Get list of user's calendars"""
    return service.get_calendar_list()

@app.post("/schedule", response_model=List[EventResponse])
async def get_schedule(
    query: ScheduleQuery,
    calendar_id: str = 'primary',
    service: CalendarService = Depends(get_calendar_service)
):
    """Get calendar schedule for specified time range"""
    
    start_time = None
    end_time = None
    
    if query.start_date:
        start_time = parser.isoparse(query.start_date)
        if start_time.tzinfo is None:
            start_time = pytz.timezone(query.timezone).localize(start_time)
    
    if query.end_date:
        end_time = parser.isoparse(query.end_date)
        if end_time.tzinfo is None:
            end_time = pytz.timezone(query.timezone).localize(end_time)
    
    events = service.get_events(
        calendar_id=calendar_id,
        start_time=start_time,
        end_time=end_time,
        max_results=query.max_results,
        timezone=query.timezone
    )
    
    # Convert to response format
    formatted_events = []
    for event in events:
        formatted_event = EventResponse(
            id=event['id'],
            summary=event.get('summary', 'No Title'),
            description=event.get('description'),
            start=event['start'],
            end=event['end'],
            location=event.get('location'),
            attendees=event.get('attendees'),
            html_link=event.get('htmlLink', '')
        )
        formatted_events.append(formatted_event)
    
    return formatted_events

@app.post("/events", response_model=EventResponse)
async def create_event(
    event_data: EventCreate,
    calendar_id: str = 'primary',
    service: CalendarService = Depends(get_calendar_service)
):
    """Create a new calendar event/meeting"""
    
    created_event = service.create_event(event_data, calendar_id)
    
    return EventResponse(
        id=created_event['id'],
        summary=created_event.get('summary', ''),
        description=created_event.get('description'),
        start=created_event['start'],
        end=created_event['end'],
        location=created_event.get('location'),
        attendees=created_event.get('attendees'),
        html_link=created_event.get('htmlLink', '')
    )

@app.put("/events/{event_id}", response_model=EventResponse)
async def update_event(
    event_id: str,
    event_data: EventCreate,
    calendar_id: str = 'primary',
    service: CalendarService = Depends(get_calendar_service)
):
    """Update an existing calendar event"""
    
    updated_event = service.update_event(event_id, event_data, calendar_id)
    
    return EventResponse(
        id=updated_event['id'],
        summary=updated_event.get('summary', ''),
        description=updated_event.get('description'),
        start=updated_event['start'],
        end=updated_event['end'],
        location=updated_event.get('location'),
        attendees=updated_event.get('attendees'),
        html_link=updated_event.get('htmlLink', '')
    )

@app.delete("/events/{event_id}")
async def delete_event(
    event_id: str,
    calendar_id: str = 'primary',
    service: CalendarService = Depends(get_calendar_service)
):
    """Delete a calendar event"""
    
    success = service.delete_event(event_id, calendar_id)
    return {"success": success, "message": "Event deleted successfully"}

@app.get("/free-time")
async def find_free_time(
    duration_minutes: int,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    timezone: str = 'UTC',
    calendar_id: str = 'primary',
    service: CalendarService = Depends(get_calendar_service)
):
    """Find available time slots for scheduling meetings"""
    
    start_time = None
    end_time = None
    
    if start_date:
        start_time = parser.isoparse(start_date)
        if start_time.tzinfo is None:
            start_time = pytz.timezone(timezone).localize(start_time)
    
    if end_date:
        end_time = parser.isoparse(end_date)
        if end_time.tzinfo is None:
            end_time = pytz.timezone(timezone).localize(end_time)
    
    free_slots = service.find_free_time(
        duration_minutes=duration_minutes,
        start_time=start_time,
        end_time=end_time,
        timezone=timezone,
        calendar_id=calendar_id
    )
    
    return {
        "duration_minutes": duration_minutes,
        "search_period": {
            "start": start_time.isoformat() if start_time else "default",
            "end": end_time.isoformat() if end_time else "default"
        },
        "free_slots": free_slots
    }

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000) 