"""
Google Calendar API Usage Examples
Demonstrates how to use the calendar API endpoints.
"""

import requests
import json
from datetime import datetime, timedelta
import pytz

# API base URL (adjust if running on different host/port)
BASE_URL = "http://localhost:8000"

def get_schedule_example():
    """Example: Get calendar schedule"""
    print("\n📅 Getting Calendar Schedule...")
    
    # Prepare request data
    schedule_data = {
        "start_date": datetime.now().isoformat(),
        "end_date": (datetime.now() + timedelta(days=7)).isoformat(),
        "timezone": "America/New_York",
        "max_results": 10
    }
    
    try:
        response = requests.post(f"{BASE_URL}/schedule", json=schedule_data)
        
        if response.status_code == 200:
            events = response.json()
            print(f"✅ Found {len(events)} upcoming events:")
            
            for event in events:
                start_time = event['start'].get('dateTime', event['start'].get('date'))
                print(f"   • {event['summary']} - {start_time}")
        else:
            print(f"❌ Error: {response.status_code} - {response.text}")
    
    except Exception as e:
        print(f"❌ Request failed: {e}")

def create_meeting_example():
    """Example: Create a new meeting"""
    print("\n📝 Creating New Meeting...")
    
    # Calculate meeting time (tomorrow at 2 PM)
    tomorrow = datetime.now() + timedelta(days=1)
    start_time = tomorrow.replace(hour=14, minute=0, second=0, microsecond=0)
    end_time = start_time + timedelta(hours=1)
    
    meeting_data = {
        "summary": "Team Standup Meeting",
        "description": "Daily team standup to discuss progress and blockers",
        "start_datetime": start_time.isoformat(),
        "end_datetime": end_time.isoformat(),
        "timezone": "America/New_York",
        "location": "Conference Room A",
        "attendees": [
            "<EMAIL>",
            "<EMAIL>"
        ]
    }
    
    try:
        response = requests.post(f"{BASE_URL}/events", json=meeting_data)
        
        if response.status_code == 200:
            event = response.json()
            print("✅ Meeting created successfully!")
            print(f"   📍 Event ID: {event['id']}")
            print(f"   📝 Title: {event['summary']}")
            print(f"   🕐 Time: {event['start']['dateTime']} - {event['end']['dateTime']}")
            print(f"   🔗 Link: {event['html_link']}")
        else:
            print(f"❌ Error: {response.status_code} - {response.text}")
    
    except Exception as e:
        print(f"❌ Request failed: {e}")

def find_free_time_example():
    """Example: Find available time slots"""
    print("\n🔍 Finding Free Time Slots...")
    
    params = {
        "duration_minutes": 60,
        "start_date": datetime.now().isoformat(),
        "end_date": (datetime.now() + timedelta(days=3)).isoformat(),
        "timezone": "America/New_York"
    }
    
    try:
        response = requests.get(f"{BASE_URL}/free-time", params=params)
        
        if response.status_code == 200:
            result = response.json()
            free_slots = result["free_slots"]
            
            print(f"✅ Found {len(free_slots)} available time slots for {params['duration_minutes']} minute meetings:")
            
            for i, slot in enumerate(free_slots[:5], 1):  # Show first 5
                start = datetime.fromisoformat(slot['start'].replace('Z', '+00:00'))
                end = datetime.fromisoformat(slot['end'].replace('Z', '+00:00'))
                print(f"   {i}. {start.strftime('%Y-%m-%d %H:%M')} - {end.strftime('%H:%M')}")
            
            if len(free_slots) > 5:
                print(f"   ... and {len(free_slots) - 5} more slots")
        else:
            print(f"❌ Error: {response.status_code} - {response.text}")
    
    except Exception as e:
        print(f"❌ Request failed: {e}")

def get_calendars_example():
    """Example: Get list of calendars"""
    print("\n📚 Getting Calendar List...")
    
    try:
        response = requests.get(f"{BASE_URL}/calendars")
        
        if response.status_code == 200:
            calendars = response.json()
            print(f"✅ Found {len(calendars)} calendars:")
            
            for calendar in calendars:
                print(f"   • {calendar.get('summary', 'Unknown')} ({calendar.get('id', 'No ID')})")
        else:
            print(f"❌ Error: {response.status_code} - {response.text}")
    
    except Exception as e:
        print(f"❌ Request failed: {e}")

def quick_meeting_scheduler():
    """Interactive meeting scheduler"""
    print("\n🚀 Quick Meeting Scheduler")
    print("=" * 30)
    
    try:
        # Get meeting details from user
        title = input("Meeting title: ") or "Quick Meeting"
        
        # Get duration
        duration_input = input("Duration in minutes (default 60): ")
        duration = int(duration_input) if duration_input.isdigit() else 60
        
        # Find free time
        print(f"\n🔍 Finding {duration}-minute slots in the next 3 days...")
        
        params = {
            "duration_minutes": duration,
            "start_date": datetime.now().isoformat(),
            "end_date": (datetime.now() + timedelta(days=3)).isoformat(),
            "timezone": "America/New_York"
        }
        
        response = requests.get(f"{BASE_URL}/free-time", params=params)
        
        if response.status_code != 200:
            print(f"❌ Error finding free time: {response.text}")
            return
        
        free_slots = response.json()["free_slots"]
        
        if not free_slots:
            print("❌ No free time slots found!")
            return
        
        print(f"\n📅 Available time slots:")
        for i, slot in enumerate(free_slots[:10], 1):  # Show first 10
            start = datetime.fromisoparse(slot['start'].replace('Z', '+00:00'))
            print(f"   {i}. {start.strftime('%Y-%m-%d %H:%M')}")
        
        # Get user choice
        choice_input = input(f"\nSelect slot (1-{min(len(free_slots), 10)}): ")
        
        if not choice_input.isdigit() or int(choice_input) < 1 or int(choice_input) > min(len(free_slots), 10):
            print("❌ Invalid selection!")
            return
        
        selected_slot = free_slots[int(choice_input) - 1]
        start_time = datetime.fromisoparse(selected_slot['start'].replace('Z', '+00:00'))
        end_time = start_time + timedelta(minutes=duration)
        
        # Create the meeting
        meeting_data = {
            "summary": title,
            "start_datetime": start_time.isoformat(),
            "end_datetime": end_time.isoformat(),
            "timezone": "America/New_York"
        }
        
        response = requests.post(f"{BASE_URL}/events", json=meeting_data)
        
        if response.status_code == 200:
            event = response.json()
            print("\n✅ Meeting scheduled successfully!")
            print(f"   📝 Title: {event['summary']}")
            print(f"   🕐 Time: {start_time.strftime('%Y-%m-%d %H:%M')} - {end_time.strftime('%H:%M')}")
            print(f"   🔗 Link: {event['html_link']}")
        else:
            print(f"❌ Error creating meeting: {response.text}")
    
    except KeyboardInterrupt:
        print("\n👋 Cancelled by user")
    except Exception as e:
        print(f"❌ Error: {e}")

def main():
    """Run example functions"""
    print("🗓️  Google Calendar API Examples")
    print("=" * 40)
    
    print("\n🔗 Make sure the Calendar API server is running:")
    print("   python calendar_api.py")
    
    # Test connection
    try:
        response = requests.get(f"{BASE_URL}/")
        if response.status_code != 200:
            print(f"❌ Cannot connect to API server at {BASE_URL}")
            return
    except Exception as e:
        print(f"❌ Cannot connect to API server: {e}")
        print("Please start the server first: python calendar_api.py")
        return
    
    print("✅ Connected to API server!")
    
    # Run examples
    get_calendars_example()
    get_schedule_example()
    find_free_time_example()
    create_meeting_example()
    
    # Interactive scheduler
    print("\n" + "="*50)
    run_scheduler = input("Run interactive meeting scheduler? (y/N): ")
    if run_scheduler.lower() == 'y':
        quick_meeting_scheduler()

if __name__ == "__main__":
    main() 