# import os
# from google.cloud import vision

# # ✅ Ensure ADC points to your service account JSON
# os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = "/Users/<USER>/Documents/hackathon/InsuranceOCR/ca-healthcareinsightsplatform-8509ba7b2e54.json"

# def detect_text(path):
#     """Detects text in the file located in `path`."""
#     client = vision.ImageAnnotatorClient()

#     with open(path, "rb") as image_file:
#         content = image_file.read()

#     image = vision.Image(content=content)

#     # Using document_text_detection for dense text (e.g. insurance documents)
#     response = client.document_text_detection(image=image)

#     if response.error.message:
#         raise Exception(f"Vision API Error: {response.error.message}")

#     # Print the full detected text
#     print("\n✅ Full OCR Text:\n")
#     print(response.full_text_annotation.text)

#     return response.full_text_annotation.text

# # 🔍 Test on your insurance image
# # image_path = "/Users/<USER>/Documents/hackathon/InsuranceOCR/data/united_healthcare.png"
# image_path = "/Users/<USER>/Downloads/Insurance Card.v1i.yolov7pytorch/train/images/IMG_2398_jpeg.rf.b19629dc9f57b55de251c710eec7f368.jpg"
# detect_text(image_path)


