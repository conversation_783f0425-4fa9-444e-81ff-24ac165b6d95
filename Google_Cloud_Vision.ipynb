{"cells": [{"cell_type": "markdown", "id": "1e802f4b", "metadata": {}, "source": ["need to install google-cloud-vision (GCP SDK) from conda -c conda-forge \n", "\n", "conda env create gcp-cloud-vision\n", "conda install -c conda-forge pillow=10.1.0 pandas=2.1.2 google-cloud-vision=3.4.5 scikit-learn=1.3.2 ipykernel jupyterlab notebook python=3.12.0\n", "\n", "to set up kernel in jupyterlabs:\n", "\n", "python -m ipykernel install --user --name=gcp-cloud-vision\n"]}, {"cell_type": "code", "execution_count": 1, "id": "5e49fc0b", "metadata": {}, "outputs": [], "source": ["# Authentication to Google API\n", "import os\n", "import math\n", "from collections import Counter\n", "from google.cloud import vision\n", "import re\n", "\n", "os.environ['GOOGLE_APPLICATION_CREDENTIALS'] ='/Users/<USER>/Documents/hackathon/InsuranceOCR/ca-healthcareinsightsplatform-8509ba7b2e54.json'\n", "WORD = re.compile(r\"\\w+\")"]}, {"cell_type": "code", "execution_count": 2, "id": "54905fa2", "metadata": {}, "outputs": [], "source": ["def detect_text(path):\n", "    \"\"\"Detects text in the file.\"\"\"\n", "\n", "    client = vision.ImageAnnotatorClient()\n", "\n", "    with open(path, \"rb\") as image_file:\n", "        content = image_file.read()\n", "\n", "    image = vision.Image(content=content)\n", "\n", "    # for non-dense text \n", "    # response = client.text_detection(image=image)\n", "    # for dense text\n", "    response = client.document_text_detection(image=image)\n", "    texts = response.text_annotations\n", "    ocr_text = []\n", "\n", "    for text in texts:\n", "        ocr_text.append(f\"\\r\\n{text.description}\")\n", "\n", "    if response.error.message:\n", "        raise Exception(\n", "            \"{}\\nFor more info on error messages, check: \"\n", "            \"https://cloud.google.com/apis/design/errors\".format(response.error.message)\n", "        )\n", "    return ocr_text"]}, {"cell_type": "code", "execution_count": 3, "id": "09b08965", "metadata": {}, "outputs": [{"ename": "ServiceUnavailable", "evalue": "503 DNS resolution failed for vision.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=SRV name=_grpclb._tcp.vision.googleapis.com: Could not contact DNS servers", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m_InactiveRpcError\u001b[39m                         Traceback (most recent call last)", "\u001b[36mFile \u001b[39m\u001b[32m/opt/homebrew/lib/python3.11/site-packages/google/api_core/grpc_helpers.py:76\u001b[39m, in \u001b[36m_wrap_unary_errors.<locals>.error_remapped_callable\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m     75\u001b[39m \u001b[38;5;28;01mtry\u001b[39;00m:\n\u001b[32m---> \u001b[39m\u001b[32m76\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mcallable_\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     77\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m grpc.RpcError \u001b[38;5;28;01mas\u001b[39;00m exc:\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/homebrew/lib/python3.11/site-packages/grpc/_channel.py:1178\u001b[39m, in \u001b[36m_UnaryUnaryMultiCallable.__call__\u001b[39m\u001b[34m(self, request, timeout, metadata, credentials, wait_for_ready, compression)\u001b[39m\n\u001b[32m   1175\u001b[39m state, call = \u001b[38;5;28mself\u001b[39m._blocking(\n\u001b[32m   1176\u001b[39m     request, timeout, metadata, credentials, wait_for_ready, compression\n\u001b[32m   1177\u001b[39m )\n\u001b[32m-> \u001b[39m\u001b[32m1178\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_end_unary_response_blocking\u001b[49m\u001b[43m(\u001b[49m\u001b[43mstate\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mcall\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01m<PERSON><PERSON><PERSON>\u001b[39;49;00m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mN<PERSON>\u001b[39;49;00m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/homebrew/lib/python3.11/site-packages/grpc/_channel.py:1006\u001b[39m, in \u001b[36m_end_unary_response_blocking\u001b[39m\u001b[34m(state, call, with_call, deadline)\u001b[39m\n\u001b[32m   1005\u001b[39m \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m-> \u001b[39m\u001b[32m1006\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m _InactiveRpcError(state)\n", "\u001b[31m_InactiveRpcError\u001b[39m: <_InactiveRpcError of RPC that terminated with:\n\tstatus = StatusCode.UNAVAILABLE\n\tdetails = \"DNS resolution failed for vision.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=SRV name=_grpclb._tcp.vision.googleapis.com: Could not contact DNS servers\"\n\tdebug_error_string = \"UNKNOWN:Error received from peer  {grpc_status:14, grpc_message:\"DNS resolution failed for vision.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=SRV name=_grpclb._tcp.vision.googleapis.com: Could not contact DNS servers\"}\"\n>", "\nThe above exception was the direct cause of the following exception:\n", "\u001b[31mServiceUnavailable\u001b[39m                        <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[3]\u001b[39m\u001b[32m, line 2\u001b[39m\n\u001b[32m      1\u001b[39m image_path = \u001b[33m\"\u001b[39m\u001b[33m/Users/<USER>/Documents/hackathon/InsuranceOCR/data/united_healthcare.png\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m----> \u001b[39m\u001b[32m2\u001b[39m text = \u001b[43mdetect_text\u001b[49m\u001b[43m(\u001b[49m\u001b[43mimage_path\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[2]\u001b[39m\u001b[32m, line 14\u001b[39m, in \u001b[36mdetect_text\u001b[39m\u001b[34m(path)\u001b[39m\n\u001b[32m      9\u001b[39m image = vision.Image(content=content)\n\u001b[32m     11\u001b[39m \u001b[38;5;66;03m# for non-dense text \u001b[39;00m\n\u001b[32m     12\u001b[39m \u001b[38;5;66;03m# response = client.text_detection(image=image)\u001b[39;00m\n\u001b[32m     13\u001b[39m \u001b[38;5;66;03m# for dense text\u001b[39;00m\n\u001b[32m---> \u001b[39m\u001b[32m14\u001b[39m response = \u001b[43mclient\u001b[49m\u001b[43m.\u001b[49m\u001b[43mdocument_text_detection\u001b[49m\u001b[43m(\u001b[49m\u001b[43mimage\u001b[49m\u001b[43m=\u001b[49m\u001b[43mimage\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     15\u001b[39m texts = response.text_annotations\n\u001b[32m     16\u001b[39m ocr_text = []\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/homebrew/lib/python3.11/site-packages/google/cloud/vision_helpers/decorators.py:112\u001b[39m, in \u001b[36m_create_single_feature_method.<locals>.inner\u001b[39m\u001b[34m(self, image, max_results, retry, timeout, metadata, **kwargs)\u001b[39m\n\u001b[32m    110\u001b[39m     copied_features[\u001b[33m\"\u001b[39m\u001b[33mmax_results\u001b[39m\u001b[33m\"\u001b[39m] = max_results\n\u001b[32m    111\u001b[39m request = \u001b[38;5;28mdict\u001b[39m(image=image, features=[copied_features], **kwargs)\n\u001b[32m--> \u001b[39m\u001b[32m112\u001b[39m response = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mannotate_image\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    113\u001b[39m \u001b[43m    \u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mretry\u001b[49m\u001b[43m=\u001b[49m\u001b[43mretry\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmetadata\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmetadata\u001b[49m\n\u001b[32m    114\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    115\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m response\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/homebrew/lib/python3.11/site-packages/google/cloud/vision_helpers/__init__.py:76\u001b[39m, in \u001b[36mVisionHelpers.annotate_image\u001b[39m\u001b[34m(self, request, retry, timeout, metadata)\u001b[39m\n\u001b[32m     74\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28mlen\u001b[39m(request.features) == \u001b[32m0\u001b[39m:\n\u001b[32m     75\u001b[39m     request.features = \u001b[38;5;28mself\u001b[39m._get_all_features()\n\u001b[32m---> \u001b[39m\u001b[32m76\u001b[39m r = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mbatch_annotate_images\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m     77\u001b[39m \u001b[43m    \u001b[49m\u001b[43mrequests\u001b[49m\u001b[43m=\u001b[49m\u001b[43m[\u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mretry\u001b[49m\u001b[43m=\u001b[49m\u001b[43mretry\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mmetadata\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmetadata\u001b[49m\n\u001b[32m     78\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     79\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m r.responses[\u001b[32m0\u001b[39m]\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/homebrew/lib/python3.11/site-packages/google/cloud/vision_v1/services/image_annotator/client.py:564\u001b[39m, in \u001b[36mImageAnnotatorClient.batch_annotate_images\u001b[39m\u001b[34m(self, request, requests, retry, timeout, metadata)\u001b[39m\n\u001b[32m    561\u001b[39m rpc = \u001b[38;5;28mself\u001b[39m._transport._wrapped_methods[\u001b[38;5;28mself\u001b[39m._transport.batch_annotate_images]\n\u001b[32m    563\u001b[39m \u001b[38;5;66;03m# Send the request.\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m564\u001b[39m response = \u001b[43mrpc\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m    565\u001b[39m \u001b[43m    \u001b[49m\u001b[43mrequest\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    566\u001b[39m \u001b[43m    \u001b[49m\u001b[43mretry\u001b[49m\u001b[43m=\u001b[49m\u001b[43mretry\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    567\u001b[39m \u001b[43m    \u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m=\u001b[49m\u001b[43mtimeout\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    568\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmetadata\u001b[49m\u001b[43m=\u001b[49m\u001b[43mmetadata\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    569\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    571\u001b[39m \u001b[38;5;66;03m# Done; return the response.\u001b[39;00m\n\u001b[32m    572\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m response\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/homebrew/lib/python3.11/site-packages/google/api_core/gapic_v1/method.py:131\u001b[39m, in \u001b[36m_GapicCallable.__call__\u001b[39m\u001b[34m(self, timeout, retry, compression, *args, **kwargs)\u001b[39m\n\u001b[32m    128\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m._compression \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m:\n\u001b[32m    129\u001b[39m     kwargs[\u001b[33m\"\u001b[39m\u001b[33mcompression\u001b[39m\u001b[33m\"\u001b[39m] = compression\n\u001b[32m--> \u001b[39m\u001b[32m131\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mwrapped_func\u001b[49m\u001b[43m(\u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m/opt/homebrew/lib/python3.11/site-packages/google/api_core/grpc_helpers.py:78\u001b[39m, in \u001b[36m_wrap_unary_errors.<locals>.error_remapped_callable\u001b[39m\u001b[34m(*args, **kwargs)\u001b[39m\n\u001b[32m     76\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m callable_(*args, **kwargs)\n\u001b[32m     77\u001b[39m \u001b[38;5;28;01mexcept\u001b[39;00m grpc.RpcError \u001b[38;5;28;01mas\u001b[39;00m exc:\n\u001b[32m---> \u001b[39m\u001b[32m78\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m exceptions.from_grpc_error(exc) \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mexc\u001b[39;00m\n", "\u001b[31mServiceUnavailable\u001b[39m: 503 DNS resolution failed for vision.googleapis.com:443: C-ares status is not ARES_SUCCESS qtype=SRV name=_grpclb._tcp.vision.googleapis.com: Could not contact DNS servers"]}], "source": ["image_path = \"/Users/<USER>/Documents/hackathon/InsuranceOCR/data/united_healthcare.png\"\n", "text = detect_text(image_path)"]}, {"cell_type": "code", "execution_count": null, "id": "4e72eb67-a4cf-4f86-bc55-3ae01e6c9e54", "metadata": {"scrolled": true}, "outputs": [], "source": ["text[0]"]}, {"cell_type": "code", "execution_count": null, "id": "c92fbd52-22e9-48f5-a81b-3c9d231b3157", "metadata": {"scrolled": true}, "outputs": [], "source": ["for line in text:\n", "    print(line)"]}, {"cell_type": "code", "execution_count": null, "id": "28ea91a5", "metadata": {"scrolled": true}, "outputs": [], "source": ["from PIL import Image, ImageDraw\n", "image=Image.open(image_path)\n", "image"]}, {"cell_type": "code", "execution_count": null, "id": "21f0a2a8-0e43-49d4-8334-7c158381f5e1", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "4ce51940-f71c-4013-bd06-91e2372398e4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "7dac4f57-9e05-4c56-8654-740ebba27bc9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2ff6f56d-8da7-4750-bd11-0aae6ecda911", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "45bcd072-6e98-45a0-b3ec-d2ca51117161", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "06dd3f93-eb61-44b7-b24b-8efacff834be", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "d73eadd3-4d2c-4280-b195-a79df861d3d9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "22b85cc1-4f4e-4e26-9de7-ebed0df89454", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "2f47c625-e723-40ed-816d-a3bbcc6fe3c9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8d911a73-5f42-4b8d-914f-7e0faff587d9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "01ef81a3-f629-4007-a1a4-ef399c0e1f16", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ea76427e-1cda-417e-90ae-da5b65ff7b43", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "640ee444", "metadata": {}, "outputs": [], "source": ["def get_cosine(vec1, vec2):\n", "    intersection = set(vec1.keys()) & set(vec2.keys())\n", "    numerator = sum([vec1[x] * vec2[x] for x in intersection])\n", "\n", "    sum1 = sum([vec1[x] ** 2 for x in list(vec1.keys())])\n", "    sum2 = sum([vec2[x] ** 2 for x in list(vec2.keys())])\n", "    denominator = math.sqrt(sum1) * math.sqrt(sum2)\n", "\n", "    if not denominator:\n", "        return 0.0\n", "    else:\n", "        return float(numerator) / denominator\n", "\n", "\n", "def text_to_vector(text):\n", "    words = WORD.findall(text)\n", "    return Counter(words)"]}, {"cell_type": "code", "execution_count": null, "id": "916d6dc4", "metadata": {}, "outputs": [], "source": ["text1 = \"This is a foo bar sentence .\"\n", "text2 = \"This sentence is similar to a foo bar sentence .\"\n", "\n", "text1 = \"AUSTIN FORTE Notary Public - State of Colorado \"\n", "text2 = \"Austin forth Notary Public - State of Colorado \"\n", "\n", "vector1 = text_to_vector(text1)\n", "vector2 = text_to_vector(text2)\n", "\n", "cosine = get_cosine(vector1, vector2)\n", "\n", "print(\"Cosine:\", cosine)"]}, {"cell_type": "code", "execution_count": null, "id": "e103a237", "metadata": {}, "outputs": [], "source": ["# print(cosine_similarity(vector1, vector2))"]}, {"cell_type": "code", "execution_count": null, "id": "dcf0adfc", "metadata": {}, "outputs": [], "source": ["# vision_client = vision.ImageAnnotatorClient()\n", "\n", "# image_path = \"content/im25.jpeg\"\n", "\n", "# with open(image_path, \"rb\") as image_file:\n", "#     content = image_file.read()\n", "        \n", "# image = vision.Image(content=content)\n", "\n", "# # image_uri = 'https://upload.wikimedia.org/wikipedia/commons/b/bf/Mobile_phone_IMEI.jpg'\n", "\n", "# # image.source.image_uri = image_uri"]}, {"cell_type": "code", "execution_count": null, "id": "e2254c47", "metadata": {}, "outputs": [], "source": ["# response = vision_client.text_detection(image=image)"]}, {"cell_type": "code", "execution_count": null, "id": "67d8e63b", "metadata": {}, "outputs": [], "source": ["# response.text_annotations[0]"]}, {"cell_type": "code", "execution_count": null, "id": "35dcd859", "metadata": {}, "outputs": [], "source": ["# text = response.text_annotations[0].description\n", "\n", "# imeis = re.findall('[0-9]{14,15}', text)\n", "\n", "# print(imeis)"]}, {"cell_type": "code", "execution_count": null, "id": "fdb4c80c", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.13"}}, "nbformat": 4, "nbformat_minor": 5}